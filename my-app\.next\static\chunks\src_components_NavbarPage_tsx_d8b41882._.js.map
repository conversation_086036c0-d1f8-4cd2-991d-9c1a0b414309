{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/NavbarPage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { AiOutlineMenu, AiOutlineClose } from \"react-icons/ai\";\r\nimport {\r\n  FaFacebookF,\r\n  FaMapMarkerAlt,\r\n  FaEnvelope,\r\n  FaPhone,\r\n} from \"react-icons/fa\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\ntype NavItem = { href: string; label: string };\r\nconst navItems: NavItem[] = [\r\n  { href: \"/\", label: \"Home\" },\r\n  { href: \"/about\", label: \"About\" },\r\n  { href: \"/service\", label: \"Services\" },\r\n  { href: \"/contact\", label: \"Contact\" },\r\n];\r\n\r\nconst NavbarPage: React.FC = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [showCompact, setShowCompact] = useState(false);\r\n  const heroRef = useRef<HTMLElement>(null);\r\n\r\n  const toggleMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);\r\n\r\n  useEffect(() => {\r\n    if (!heroRef.current) return;\r\n\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        setShowCompact(!entry.isIntersecting); // show compact navbar when hero is out of view\r\n      },\r\n      { threshold: 0 }\r\n    );\r\n\r\n    observer.observe(heroRef.current);\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      \r\n      <section ref={heroRef} className=\"min-h-[20vh] md:min-h-[0vh] bg-white\">\r\n       \r\n        <nav className=\"w-full\">\r\n         \r\n          <div className=\"flex items-center justify-between px-6 py-5 max-w-7xl mx-auto\">\r\n            <Link href=\"/\">\r\n              <Image src=\"/logo.png\" alt=\"Logo\" width={180} height={50} />\r\n            </Link>\r\n\r\n            <div className=\"hidden md:flex items-center space-x-10\">\r\n              <div className=\"flex items-center space-x-3 px-2\">\r\n                <div className=\"w-12 h-12 bg-[#272fcc] rounded-full flex items-center justify-center\">\r\n                  <FaMapMarkerAlt className=\"text-white\" size={16} />\r\n                </div>\r\n                <div>\r\n                  <div className=\"font-semibold text-gray-900\">\r\n                    A 32 / 2 Rabindrapally Garia\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">Visit Our Office</div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-3 px-2\">\r\n                <div className=\"w-12 h-12 bg-[#272fcc] rounded-full flex items-center justify-center\">\r\n                  <FaEnvelope className=\"text-white\" size={16} />\r\n                </div>\r\n                <div>\r\n                  <div className=\"font-semibold text-gray-900\">\r\n                    <EMAIL>\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">Email Address</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n           \r\n            <div className=\"flex md:hidden\">\r\n              <button\r\n                onClick={toggleMenu}\r\n                aria-label=\"Open menu\"\r\n                className=\"p-2 rounded-md text-gray-700 hover:text-blue-500\"\r\n              >\r\n                <AiOutlineMenu size={24} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          \r\n          <div className=\"hidden md:flex border-t border-gray-200 px-6\">\r\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto w-full py-4\">\r\n              <div className=\"flex space-x-8\">\r\n                {navItems.map((item) => (\r\n                  <Link\r\n                    key={item.href}\r\n                    href={item.href}\r\n                    className=\"px-3 py-2 text-gray-700 hover:text-blue-500 font-medium transition-colors\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n              <Link\r\n                href=\"/contact\"\r\n                className=\"bg-[#272fcc] hover:bg-blue-800 rounded-md text-white px-6 py-3 font-semibold transition-colors\"\r\n              >\r\n               Sign in\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </nav>\r\n      </section>\r\n\r\n      \r\n      {showCompact && (\r\n        <nav className=\"fixed top-0 left-0 w-full z-50 bg-white border-b border-gray-300 shadow-sm transition-all duration-300\">\r\n          <div className=\"max-w-7xl mx-auto px-6 py-3 flex justify-between items-center\">\r\n            <Link href=\"/\">\r\n              <Image src=\"/logo.png\" alt=\"Logo\" width={140} height={40} />\r\n            </Link>\r\n            <div className=\"hidden md:flex items-center space-x-6\">\r\n              {navItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className=\"text-gray-700 hover:text-blue-500 text-sm font-medium transition-colors\"\r\n                >\r\n                  {item.label}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n            <div className=\"md:hidden flex items-center\">\r\n              <button\r\n                onClick={toggleMenu}\r\n                className=\"p-2 rounded-md text-gray-700 hover:text-blue-500\"\r\n              >\r\n                <AiOutlineMenu size={24} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </nav>\r\n      )}\r\n\r\n      \r\n      <div\r\n        className={`fixed top-0 left-0 h-full bg-white z-50 w-80 shadow-lg transform transition-transform duration-300 ease-in-out ${\r\n          isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full\"\r\n        }`}\r\n      >\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center justify-between py-2 mb-8\">\r\n            <Image src=\"/logo.png\" alt=\"Logo\" width={120} height={32} />\r\n            <button\r\n              onClick={toggleMenu}\r\n              aria-label=\"Close menu\"\r\n              className=\"p-2 text-gray-600 hover:text-gray-900\"\r\n            >\r\n              <AiOutlineClose size={24} />\r\n            </button>\r\n          </div>\r\n          <nav className=\"space-y-2\">\r\n            {navItems.map((item) => (\r\n              <Link\r\n                key={item.href}\r\n                href={item.href}\r\n                className=\"block px-2 py-3 text-gray-700 hover:text-orange-500 text-lg font-medium\"\r\n                onClick={() => setIsMobileMenuOpen(false)}\r\n              >\r\n                {item.label}\r\n              </Link>\r\n            ))}\r\n          </nav>\r\n          <div className=\"mt-8\">\r\n            <Link\r\n              href=\"/contact\"\r\n              className=\"block w-full text-center bg-[#272fcc] hover:bg-blue-800 text-white py-3 font-semibold transition-colors rounded-md\"\r\n              onClick={() => setIsMobileMenuOpen(false)}\r\n            >\r\n              Contact Us\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {isMobileMenuOpen && (\r\n        <div className=\"fixed inset-0 bg-black/60 z-40\" onClick={toggleMenu} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NavbarPage;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAMA;AACA;;;AAXA;;;;;;AAcA,MAAM,WAAsB;IAC1B;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAY,OAAO;IAAW;IACtC;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAED,MAAM,aAAuB;;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAC;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,UAAU,IAAA,uKAAM,EAAc;IAEpC,MAAM,aAAa,IAAM,oBAAoB,CAAC;IAE9C,IAAA,0KAAS;gCAAC;YACR,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,MAAM,WAAW,IAAI;wCACnB;wBAAC,CAAC,MAAM;oBACN,eAAe,CAAC,MAAM,cAAc,GAAG,+CAA+C;gBACxF;uCACA;gBAAE,WAAW;YAAE;YAGjB,SAAS,OAAO,CAAC,QAAQ,OAAO;YAEhC;wCAAO,IAAM,SAAS,UAAU;;QAClC;+BAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC;gBAAQ,KAAK;gBAAS,WAAU;0BAE/B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0KAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,2IAAK;wCAAC,KAAI;wCAAY,KAAI;wCAAO,OAAO;wCAAK,QAAQ;;;;;;;;;;;8CAGxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mKAAc;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAE/C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA8B;;;;;;sEAG7C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAU;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAE3C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA8B;;;;;;sEAG7C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,cAAW;wCACX,WAAU;kDAEV,cAAA,6LAAC,kKAAa;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,0KAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;+CAJN,KAAK,IAAI;;;;;;;;;;kDAQpB,6LAAC,0KAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0KAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,2IAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAK,QAAQ;;;;;;;;;;;sCAExD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,0KAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAQpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,kKAAa;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,6LAAC;gBACC,WAAW,AAAC,kHAEX,OADC,mBAAmB,kBAAkB;0BAGvC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2IAAK;oCAAC,KAAI;oCAAY,KAAI;oCAAO,OAAO;oCAAK,QAAQ;;;;;;8CACtD,6LAAC;oCACC,SAAS;oCACT,cAAW;oCACX,WAAU;8CAEV,cAAA,6LAAC,mKAAc;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAG1B,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,0KAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB;0CACpC;;;;;;;;;;;;;;;;;;;;;;YAON,kCACC,6LAAC;gBAAI,WAAU;gBAAiC,SAAS;;;;;;;;AAIjE;GA5KM;KAAA;uCA8KS", "debugId": null}}]}