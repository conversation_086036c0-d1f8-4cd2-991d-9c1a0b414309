"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

export function WhyChooseUsSection() {
  const whyChooseUsPoints = [
    {
      icon: (
        <svg className="w-8 h-8 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      image: "/images/hvac-breakdown-recovery.jpg",
      title: "Fast Breakdown Recovery",
      description: "We understand the importance of breakdown recovery lead time to our customers.",
      highlight: "Quick Response"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      image: "/images/hvac-system-maintenance.jpg",
      title: "Reliable System & Manpower",
      description: "Our unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.",
      highlight: "On-Time Delivery"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      image: "/images/hvac-st-maintenance.jpg",
      title: "ST HVAC Maintenance Method",
      description: "Our unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.",
      highlight: "Proven Method"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      image: "/images/hvac-competitive-rates.jpg",
      title: "Competitive Rates",
      description: "Our Rates are very Competitive with no compromise attitude as far as quality is concerned.",
      highlight: "Best Value"
    }
  ];

  const specialtyServices = [
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      ),
      image: "/images/commercial-hvac.jpg",
      title: "Commercial HVAC",
      description: "Complete commercial heating and cooling solutions"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      image: "/images/residential-hvac.jpg",
      title: "Residential HVAC",
      description: "Home comfort solutions and energy efficiency"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      image: "/images/maintenance-plans.jpg",
      title: "Maintenance Plans",
      description: "Preventive maintenance and service contracts"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      image: "/images/energy-audits.jpg",
      title: "Energy Audits",
      description: "Efficiency assessments and optimization"
    }
  ];

  return (
    <section className="relative py-20 lg:py-32 bg-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-cyan-500 via-blue-500 to-purple-600"></div>
        <div className="absolute top-0 left-0 w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10">
        {/* Main Title Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-block relative">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-black mb-6 bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-600 bg-clip-text text-transparent leading-tight">
              WHY <span className="text-gray-900">YOU WILL</span><br />
              <span className="text-gray-900">CHOOSE</span> US?
            </h1>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"></div>
          </div>
        </motion.div>

        {/* Main Content - Diagonal Layout */}
        <div className="relative mb-32">
          {/* Diagonal Background Elements */}
          <div className="absolute inset-0 transform -skew-y-2 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-3xl"></div>

          <div className="relative z-10 py-16">
            {/* Content Cards in Staggered Layout */}
            <div className="space-y-16">
              {/* First Point - Left Aligned */}
              <motion.div
                initial={{ opacity: 0, x: -100 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16"
              >
                <div className="lg:w-1/2 order-2 lg:order-1">
                  <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20"></div>
                    <div className="relative bg-white rounded-2xl p-8 shadow-2xl border border-gray-100">
                      <div className="flex items-start gap-6">
                        <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                          <span className="text-white text-2xl font-black">W</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 leading-relaxed">
                            <span className="text-cyan-600">W</span>e understand the importance of breakdown recovery lead time to our customers.
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="lg:w-1/2 order-1 lg:order-2">
                  <div className="relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-2xl">
                    <Image
                      src="/Picture1.jpg"
                      alt="HVAC Breakdown Recovery"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-cyan-900/60 to-transparent"></div>
                  </div>
                </div>
              </motion.div>

              {/* Second Point - Right Aligned */}
              <motion.div
                initial={{ opacity: 0, x: 100 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16"
              >
                <div className="lg:w-1/2">
                  <div className="relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-2xl">
                    <Image
                      src="/Picture2.jpg"
                      alt="HVAC System and Manpower"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-blue-900/60 to-transparent"></div>
                  </div>
                </div>
                <div className="lg:w-1/2">
                  <div className="relative">
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20"></div>
                    <div className="relative bg-white rounded-2xl p-8 shadow-2xl border border-gray-100">
                      <div className="flex items-start gap-6">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                          <span className="text-white text-2xl font-black">O</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 leading-relaxed">
                            <span className="text-blue-600">O</span>ur unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Third Point - Left Aligned */}
              <motion.div
                initial={{ opacity: 0, x: -100 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16"
              >
                <div className="lg:w-1/2 order-2 lg:order-1">
                  <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-20"></div>
                    <div className="relative bg-white rounded-2xl p-8 shadow-2xl border border-gray-100">
                      <div className="flex items-start gap-6">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                          <span className="text-white text-2xl font-black">O</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 leading-relaxed">
                            <span className="text-purple-600">O</span>ur unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="lg:w-1/2 order-1 lg:order-2">
                  <div className="relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-2xl">
                    <Image
                      src="/Picture3.jpg"
                      alt="ST HVAC Maintenance Method"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 to-transparent"></div>
                  </div>
                </div>
              </motion.div>

              {/* Fourth Point - Right Aligned */}
              <motion.div
                initial={{ opacity: 0, x: 100 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16"
              >
                <div className="lg:w-1/2">
                  <div className="relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-2xl">
                    <Image
                      src="/Picture4.jpg"
                      alt="Competitive Rates"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-green-900/60 to-transparent"></div>
                  </div>
                </div>
                <div className="lg:w-1/2">
                  <div className="relative">
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full opacity-20"></div>
                    <div className="relative bg-white rounded-2xl p-8 shadow-2xl border border-gray-100">
                      <div className="flex items-start gap-6">
                        <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                          <span className="text-white text-2xl font-black">O</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 leading-relaxed">
                            <span className="text-green-600">O</span>ur Rates are very Competitive with no compromise attitude as far as quality is concerned.
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Specialty Section - Hexagonal Design */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
          className="relative"
        >
          {/* Specialty Title with Geometric Design */}
          <div className="text-center mb-20">
            <div className="relative inline-block">
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-3xl transform rotate-3 opacity-20"></div>
              <div className="relative bg-white rounded-3xl px-12 py-8 shadow-2xl border border-gray-100">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-black">
                  <span className="bg-gradient-to-r from-cyan-600 to-purple-600 bg-clip-text text-transparent">SPECIALTY</span>
                  <span className="text-gray-900"> WITHIN</span>
                </h2>
              </div>
            </div>
          </div>

          {/* Central Content Hub */}
          <div className="relative max-w-6xl mx-auto">
            {/* Central Text Circle */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20"
            >
              <div className="w-80 h-80 bg-gradient-to-br from-white via-cyan-50 to-blue-50 rounded-full shadow-2xl border-4 border-white flex items-center justify-center p-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 leading-tight">
                    <span className="text-cyan-600">O</span>ur all locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on. Easy access is further enhanced through Service Solutions Providers, minimizing down-time and ensuring quick satisfaction.
                  </h3>
                </div>
              </div>
            </motion.div>

            {/* Orbiting Service Cards */}
            <div className="relative h-96 lg:h-[600px]">
              {specialtyServices.map((service, index) => {
                const angle = (index * 90) - 45; // 90 degrees apart, starting at -45
                const radius = 280;
                const x = Math.cos((angle * Math.PI) / 180) * radius;
                const y = Math.sin((angle * Math.PI) / 180) * radius;

                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.5, rotate: -angle }}
                    whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 + index * 0.2 }}
                    viewport={{ once: true }}
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      transform: `translate(-50%, -50%) translate(${x}px, ${y}px)`,
                    }}
                  >
                    <div className="group relative">
                      <div className="w-48 h-48 rounded-2xl overflow-hidden shadow-2xl border-4 border-white">
                        <Image
                          src={`/Picture${index + 5}.jpg`}
                          alt={service.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                        <div className="absolute inset-0 flex flex-col justify-between p-4">
                          <div className="flex justify-center">
                            <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                              {service.icon}
                            </div>
                          </div>
                          <div className="text-center">
                            <h4 className="text-white font-bold text-sm mb-1">
                              {service.title}
                            </h4>
                            <p className="text-white/80 text-xs leading-tight">
                              {service.description}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Connecting Line */}
                      <div
                        className="absolute top-1/2 left-1/2 w-1 bg-gradient-to-r from-cyan-400 to-blue-500 opacity-30"
                        style={{
                          height: `${radius - 120}px`,
                          transformOrigin: 'top center',
                          transform: `translate(-50%, -50%) rotate(${angle + 180}deg)`,
                        }}
                      ></div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Background Geometric Shapes */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-cyan-400/10 to-blue-500/10 rounded-full"></div>
            <div className="absolute bottom-10 right-10 w-48 h-48 bg-gradient-to-br from-purple-400/10 to-pink-500/10 rounded-full"></div>
            <div className="absolute top-1/3 right-20 w-24 h-24 bg-gradient-to-br from-green-400/10 to-emerald-500/10 transform rotate-45"></div>
            <div className="absolute bottom-1/3 left-20 w-36 h-36 bg-gradient-to-br from-orange-400/10 to-red-500/10 transform rotate-12"></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
