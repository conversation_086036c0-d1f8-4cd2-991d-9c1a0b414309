"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

export function WhyChooseUsSection() {

  const specialtyServices = [
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      ),
      image: "/images/commercial-hvac.jpg",
      title: "Commercial HVAC",
      description: "Complete commercial heating and cooling solutions"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      image: "/images/residential-hvac.jpg",
      title: "Residential HVAC",
      description: "Home comfort solutions and energy efficiency"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      image: "/images/maintenance-plans.jpg",
      title: "Maintenance Plans",
      description: "Preventive maintenance and service contracts"
    },
    {
      icon: (
        <svg className="w-6 h-6 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      image: "/images/energy-audits.jpg",
      title: "Energy Audits",
      description: "Efficiency assessments and optimization"
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-gray-100">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Main Layout - Left Content, Right Image */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Header */}
            <div className="mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Why Choose Us
              </h1>
              <p className="text-gray-600 text-lg">
                Our commitment to excellence and customer satisfaction
              </p>
            </div>

            {/* Numbered Points Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Point 01 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-gray-900">01</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Fast Breakdown Recovery
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">W</span>e understand the importance of breakdown recovery lead time to our customers.
                </p>
              </motion.div>

              {/* Point 02 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-gray-900">02</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Reliable System & Manpower
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.
                </p>
              </motion.div>

              {/* Point 03 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-gray-900">03</div>
                <h3 className="text-xl font-bold text-gray-900">
                  ST HVAC Maintenance Method
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.
                </p>
              </motion.div>

              {/* Point 04 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-gray-900">04</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Competitive Rates
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur Rates are very Competitive with no compromise attitude as far as quality is concerned.
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column - Large Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative h-[500px] lg:h-[600px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/Picture1.jpg"
                alt="Professional HVAC Services"
                fill
                className="object-cover"
              />
              {/* Decorative Elements */}
              <div className="absolute top-8 right-8 w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full border border-white/30"></div>
              <div className="absolute bottom-8 left-8 w-16 h-16 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30"></div>
            </div>
          </motion.div>
        </div>

        {/* Specialty Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-20 bg-white rounded-2xl p-8 lg:p-12 shadow-lg"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="text-blue-600">SPECIALTY</span> WITHIN
            </h2>
            <div className="w-24 h-1 bg-blue-600 mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left - Main Text */}
            <div className="space-y-6">
              <div className="bg-blue-50 rounded-xl p-8 border border-blue-100">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 leading-relaxed">
                  <span className="text-blue-600">O</span>ur all locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on. Easy access is further enhanced through Service Solutions Providers, minimizing down-time and ensuring quick satisfaction.
                </h3>
              </div>
            </div>

            {/* Right - Services Grid */}
            <div className="grid grid-cols-2 gap-4">
              {specialtyServices.map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-50 rounded-lg p-4 text-center hover:bg-blue-50 transition-colors duration-300"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    {service.icon}
                  </div>
                  <h4 className="font-semibold text-gray-900 text-sm mb-2">
                    {service.title}
                  </h4>
                  <p className="text-gray-600 text-xs">
                    {service.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
