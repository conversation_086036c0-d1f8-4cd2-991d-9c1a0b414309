"use client";

import React, { useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

type GalleryImage = {
  src: string;
  alt: string;
  category: string;
  description: string;
  location?: string;
  year?: string;
};

// Professional project gallery
const projectImages: GalleryImage[] = [
  {
    src: "/Picture2.jpg",
    alt: "Commercial HVAC Installation",
    category: "Commercial",
    description: "Complete HVAC system installation for a 50,000 sq ft office building including energy-efficient units and smart controls.",
    location: "Downtown Office Complex",
    year: "2024"
  },
  {
    src: "/Picture4.jpg",
    alt: "Industrial Ductwork System",
    category: "Industrial",
    description: "Custom ductwork design and installation for manufacturing facility with specialized ventilation requirements.",
    location: "Manufacturing Plant",
    year: "2023"
  },
  {
    src: "/Picture5.jpg",
    alt: "Smart Thermostat Installation",
    category: "Residential",
    description: "Smart thermostat and control system installation with mobile app integration and energy monitoring.",
    location: "Residential Home",
    year: "2024"
  },
  {
    src: "/Picture6.jpg",
    alt: "Residential HVAC Upgrade",
    category: "Residential",
    description: "Complete residential HVAC system replacement with high-efficiency equipment and improved air quality.",
    location: "Family Residence",
    year: "2024"
  },
  {
    src: "/Picture8.jpg",
    alt: "Preventive Maintenance Service",
    category: "Maintenance",
    description: "Comprehensive maintenance service including system inspection, cleaning, and performance optimization.",
    location: "Various Locations",
    year: "2024"
  },
  {
    src: "/Picture11.jpg",
    alt: "Energy Efficiency Retrofit",
    category: "Commercial",
    description: "Energy efficiency upgrade project resulting in 30% reduction in energy costs for retail facility.",
    location: "Retail Center",
    year: "2023"
  },
];

export default function Gallery() {
  const images = useMemo(() => projectImages, []);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [filter, setFilter] = useState<string>("All");

  const categories = ["All", ...Array.from(new Set(images.map((img: GalleryImage) => img.category)))];
  const filteredImages = filter === "All" ? images : images.filter((img: GalleryImage) => img.category === filter);

  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Professional Header */}
        <motion.header
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center space-x-2 mb-6">
            <div className="w-12 h-px bg-blue-600"></div>
            <span className="text-blue-600 font-medium text-sm uppercase tracking-wider">Project Gallery</span>
            <div className="w-12 h-px bg-blue-600"></div>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Our Recent HVAC Projects
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of successful HVAC installations, maintenance projects, and energy efficiency upgrades
            across residential, commercial, and industrial properties.
          </p>
        </motion.header>

        {/* Professional Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category: string) => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                filter === category
                  ? "bg-blue-600 text-white shadow-lg"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Professional Gallery Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredImages.map((image, index) => (
              <motion.div
                key={image.src}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="group relative cursor-pointer"
                onClick={() => setSelectedImage(image)}
              >
                <div className="relative overflow-hidden rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <img
                      src={image.src}
                      alt={image.alt}
                      loading="lazy"
                      className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Category Badge */}
                    <div className="absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded">
                      {image.category}
                    </div>

                    {/* Project Info */}
                    {image.year && (
                      <div className="absolute top-4 right-4 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded">
                        {image.year}
                      </div>
                    )}

                    {/* Content */}
                    <div className="absolute inset-x-0 bottom-0 p-6 text-white transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                      <h3 className="text-lg font-bold mb-1">{image.alt}</h3>
                      {image.location && (
                        <p className="text-sm text-white/80 mb-2">{image.location}</p>
                      )}
                      <p className="text-sm text-white/90 leading-relaxed line-clamp-2">{image.description}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Professional Lightbox Modal */}
        <AnimatePresence>
          {selectedImage && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
              onClick={() => setSelectedImage(null)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden shadow-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="relative">
                  <img
                    src={selectedImage.src}
                    alt={selectedImage.alt}
                    className="w-full h-auto max-h-[60vh] object-cover"
                  />

                  {/* Close Button */}
                  <button
                    onClick={() => setSelectedImage(null)}
                    className="absolute top-4 right-4 w-10 h-10 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                  {/* Category Badge */}
                  <div className="absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded">
                    {selectedImage.category}
                  </div>

                  {/* Year Badge */}
                  {selectedImage.year && (
                    <div className="absolute top-4 left-20 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded">
                      {selectedImage.year}
                    </div>
                  )}
                </div>

                {/* Professional Content */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-1">{selectedImage.alt}</h3>
                      {selectedImage.location && (
                        <p className="text-blue-600 font-medium">{selectedImage.location}</p>
                      )}
                    </div>
                    {selectedImage.year && (
                      <span className="text-gray-500 text-sm">{selectedImage.year}</span>
                    )}
                  </div>
                  <p className="text-gray-600 leading-relaxed">{selectedImage.description}</p>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
